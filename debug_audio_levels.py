#!/usr/bin/env python3
"""
debug script to analyze audio levels and help diagnose speech detection issues.
"""

import sys
from pydub import AudioSegment
from pydub.silence import detect_nonsilent
import numpy as np

def analyze_audio_file(audio_file_path):
    """analyze audio file and provide diagnostic information."""
    print(f"analyzing audio file: {audio_file_path}")
    print("=" * 60)
    
    try:
        # load audio file
        audio = AudioSegment.from_file(audio_file_path)
        
        # basic info
        print(f"duration: {len(audio) / 1000.0:.2f} seconds")
        print(f"channels: {audio.channels}")
        print(f"frame rate: {audio.frame_rate} hz")
        print(f"sample width: {audio.sample_width} bytes")
        
        # calculate rms levels
        audio_data = np.array(audio.get_array_of_samples())
        if audio.channels == 2:
            audio_data = audio_data.reshape((-1, 2))
            audio_data = audio_data.mean(axis=1)  # convert to mono
        
        rms = np.sqrt(np.mean(audio_data**2))
        max_amplitude = np.max(np.abs(audio_data))
        
        # convert to db
        if rms > 0:
            rms_db = 20 * np.log10(rms / (2**(audio.sample_width * 8 - 1)))
        else:
            rms_db = -float('inf')
            
        if max_amplitude > 0:
            max_db = 20 * np.log10(max_amplitude / (2**(audio.sample_width * 8 - 1)))
        else:
            max_db = -float('inf')
        
        print(f"rms level: {rms_db:.2f} db")
        print(f"peak level: {max_db:.2f} db")
        
        # test different silence thresholds
        print("\nspeech detection test with different thresholds:")
        print("-" * 50)
        
        thresholds_to_test = [-50, -40, -35, -30, -25, -20, -15, -10, -5, -2]
        
        for threshold in thresholds_to_test:
            non_silent = detect_nonsilent(
                audio, 
                min_silence_len=500,  # 0.5 seconds
                silence_thresh=threshold
            )
            
            if non_silent:
                total_speech_time = sum(end - start for start, end in non_silent) / 1000.0
                speech_percentage = (total_speech_time / (len(audio) / 1000.0)) * 100
                print(f"threshold {threshold:3d} db: {len(non_silent):2d} segments, "
                      f"{total_speech_time:5.2f}s speech ({speech_percentage:5.1f}%)")
            else:
                print(f"threshold {threshold:3d} db: no speech detected")
        
        # recommend optimal threshold
        print("\nrecommendations:")
        print("-" * 30)
        
        # find a good threshold (one that detects reasonable amount of speech)
        best_threshold = -35
        for threshold in thresholds_to_test:
            non_silent = detect_nonsilent(audio, min_silence_len=500, silence_thresh=threshold)
            if non_silent:
                total_speech_time = sum(end - start for start, end in non_silent) / 1000.0
                speech_percentage = (total_speech_time / (len(audio) / 1000.0)) * 100
                if 20 <= speech_percentage <= 90:  # reasonable speech percentage
                    best_threshold = threshold
                    break
        
        print(f"recommended silence_threshold: {best_threshold} db")
        print(f"current config threshold (0.02 * -100): -2 db (too high!)")
        print(f"suggested config threshold: {abs(best_threshold) / 100:.3f}")
        
    except Exception as e:
        print(f"error analyzing audio: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("usage: python debug_audio_levels.py <audio_file_path>")
        sys.exit(1)
    
    audio_file = sys.argv[1]
    analyze_audio_file(audio_file)
